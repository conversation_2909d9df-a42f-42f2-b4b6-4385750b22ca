#include "logger.h"
#include <iostream>
#include <memory>

int main() {
    std::cout << "=== Extended Logger System Demo ===" << std::endl;

    // 1. Create instances of all logger types
    std::cout << "\n1. Creating logger instances:" << std::endl;
    InfoLogger infoLogger;
    WarningLogger warningLogger;
    ErrorLogger errorLogger;
    DebugLogger debugLogger;

    std::cout << "All logger instances created successfully!" << std::endl;

    // 2. Polymorphism demonstration with all four logger types
    std::cout << "\n2. Polymorphism demonstration (base class pointer):" << std::endl;
    Logger* logger = nullptr;

    // Point to InfoLogger
    logger = &infoLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("Application started successfully");

    // Point to WarningLogger
    logger = &warningLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("Memory usage is high");

    // Point to ErrorLogger
    logger = &errorLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("Database connection failed");

    // Point to DebugLogger
    logger = &debugLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("Variable value: x = 42");

    // Display final statistics
    std::cout << "\n=== Final Statistics ===" << std::endl;
    std::cout << "Total log calls: " << Logger::getLogCount() << std::endl;
    std::cout << "Expected: 4 (one call for each logger type)" << std::endl;

    if (Logger::getLogCount() == 4) {
        std::cout << "✓ Log count verification: PASSED" << std::endl;
    } else {
        std::cout << "✗ Log count verification: FAILED" << std::endl;
    }

    std::cout << "\nExtended logger system demonstration completed!" << std::endl;
    return 0;
}
