#include "logger.h"
#include <iostream>
#include <memory>

int main() {
    std::cout << "=== Logger System Demo ===" << std::endl;

    // 1. Direct use of derived class objects
    std::cout << "\n1. Direct use of derived class objects:" << std::endl;
    InfoLogger infoLogger;
    ErrorLogger errorLogger;
    WarningLogger warningLogger;

    infoLogger.log("System startup successful");
    errorLogger.log("File not found");
    warningLogger.log("High memory usage");

    std::cout << "Current total log calls: " << Logger::getLogCount() << std::endl;

    // 2. Polymorphism demonstration (base class pointer)
    std::cout << "\n2. Polymorphism demonstration (base class pointer):" << std::endl;
    Logger* logger = nullptr;

    // Point to InfoLogger
    logger = &infoLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("User login successful");

    // Point to ErrorLogger
    logger = &errorLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("Database connection failed");

    // Point to WarningLogger
    logger = &warningLogger;
    std::cout << "Current logger type: " << logger->getLoggerType() << std::endl;
    logger->log("Insufficient disk space");

    // 3. Smart pointer polymorphism demonstration
    std::cout << "\n3. Smart pointer polymorphism demonstration:" << std::endl;
    std::unique_ptr<Logger> smartLogger;

    smartLogger = std::make_unique<InfoLogger>();
    smartLogger->log("Configuration file loaded");

    smartLogger = std::make_unique<ErrorLogger>();
    smartLogger->log("Network connection timeout");

    smartLogger = std::make_unique<WarningLogger>();
    smartLogger->log("High CPU usage");

    // Display final statistics
    std::cout << "\n=== Statistics ===" << std::endl;
    std::cout << "Total log calls: " << Logger::getLogCount() << std::endl;

    std::cout << "\nProgram execution completed!" << std::endl;
    return 0;
}
