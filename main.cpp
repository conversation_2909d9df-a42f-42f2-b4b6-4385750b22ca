#include "logger.h"
#include <iostream>
#include <memory>

int main() {
    std::cout << "=== 日志记录器系统演示 ===" << std::endl;
    
    // 1. 直接使用派生类对象
    std::cout << "\n1. 直接使用派生类对象：" << std::endl;
    InfoLogger infoLogger;
    ErrorLogger errorLogger;
    WarningLogger warningLogger;
    
    infoLogger.log("系统启动成功");
    errorLogger.log("文件未找到");
    warningLogger.log("内存使用率较高");
    
    std::cout << "当前日志调用总次数：" << Logger::getLogCount() << std::endl;
    
    // 2. 使用多态（基类指针）演示
    std::cout << "\n2. 使用多态（基类指针）演示：" << std::endl;
    Logger* logger = nullptr;
    
    // 指向InfoLogger
    logger = &infoLogger;
    std::cout << "当前记录器类型：" << logger->getLoggerType() << std::endl;
    logger->log("用户登录成功");
    
    // 指向ErrorLogger
    logger = &errorLogger;
    std::cout << "当前记录器类型：" << logger->getLoggerType() << std::endl;
    logger->log("数据库连接失败");
    
    // 指向WarningLogger
    logger = &warningLogger;
    std::cout << "当前记录器类型：" << logger->getLoggerType() << std::endl;
    logger->log("磁盘空间不足");
    
    // 3. 使用智能指针演示多态
    std::cout << "\n3. 使用智能指针演示多态：" << std::endl;
    std::unique_ptr<Logger> smartLogger;
    
    smartLogger = std::make_unique<InfoLogger>();
    smartLogger->log("配置文件加载完成");
    
    smartLogger = std::make_unique<ErrorLogger>();
    smartLogger->log("网络连接超时");
    
    smartLogger = std::make_unique<WarningLogger>();
    smartLogger->log("CPU使用率过高");
    
    // 显示最终统计信息
    std::cout << "\n=== 统计信息 ===" << std::endl;
    std::cout << "总日志调用次数：" << Logger::getLogCount() << std::endl;
    
    std::cout << "\n程序执行完毕！" << std::endl;
    return 0;
}
