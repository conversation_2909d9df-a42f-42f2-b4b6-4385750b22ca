{"version": "2.0.0", "tasks": [{"label": "build", "type": "shell", "command": "g++", "args": ["-g", "-std=c++17", "-Wall", "-Wextra", "main.cpp", "logger.cpp", "-o", "bin/logger_system.exe"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$gcc"], "detail": "编译多文件C++项目到bin目录"}, {"label": "clean", "type": "shell", "command": "powershell", "args": ["-Command", "if (Test-Path 'bin/logger_system.exe') { Remove-Item 'bin/logger_system.exe' -Force; Write-Host '清理完成' } else { Write-Host '没有找到可执行文件' }"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "detail": "清理编译生成的文件"}, {"type": "cppbuild", "label": "C/C++: g++.exe 生成活动文件", "command": "C:\\msys64\\ucrt64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}]}