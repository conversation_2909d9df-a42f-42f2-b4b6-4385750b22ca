#ifndef LOGGER_H
#define LOGGER_H

#include <string>

// Logger基类
class Logger {
protected:
    static int logCount;  // 静态成员变量，记录所有日志调用的总次数

public:
    // 纯虚函数，派生类必须实现
    virtual void log(const std::string& message) = 0;
    
    // 静态成员函数，返回日志总次数
    static int getLogCount();
    
    // const成员函数，返回记录器类型
    virtual const char* getLoggerType() const;
    
    // 虚析构函数
    virtual ~Logger() = default;
};

// InfoLogger派生类
class InfoLogger : public Logger {
public:
    void log(const std::string& message) override;
    const char* getLoggerType() const override;
};

// ErrorLogger派生类
class ErrorLogger : public Logger {
public:
    void log(const std::string& message) override;
    const char* getLoggerType() const override;
};

// WarningLogger派生类
class WarningLogger : public Logger {
public:
    void log(const std::string& message) override;
    const char* getLoggerType() const override;
};

// DebugLogger派生类
class DebugLogger : public Logger {
public:
    void log(const std::string& message) override;
    const char* getLoggerType() const override;
};

#endif // LOGGER_H
