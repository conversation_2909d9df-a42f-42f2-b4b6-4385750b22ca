# 日志记录器系统

## 项目概述
这是一个基于C++面向对象设计的日志记录器系统，支持不同等级的日志记录（信息、警告、错误）。

## 项目结构
```
LanQiao/
├── main.cpp          # 主程序文件，演示多态使用
├── logger.h          # Logger基类和派生类声明
├── logger.cpp        # Logger类的实现
├── bin/              # 编译生成的可执行文件目录
│   └── logger_system.exe
└── .vscode/          # VSCode配置文件
    ├── tasks.json    # 编译任务配置
    └── launch.json   # 调试配置
```

## 设计思路

### 1. 类层次结构
- **Logger基类**: 抽象基类，定义了日志记录的通用接口
  - 包含纯虚函数 `log(const std::string& message)`
  - 静态成员变量 `logCount` 记录总调用次数
  - 静态成员函数 `getLogCount()` 获取调用次数
  - 虚函数 `getLoggerType()` 返回记录器类型

- **派生类**: 
  - `InfoLogger`: 输出 `[INFO]: message` 格式
  - `ErrorLogger`: 输出 `[ERROR]: message` 格式  
  - `WarningLogger`: 输出 `[WARNING]: message` 格式

### 2. 核心C++特性应用
- **多态**: 通过基类指针调用派生类方法
- **纯虚函数**: 强制派生类实现log方法
- **静态成员**: logCount统计所有实例的调用次数
- **const引用传参**: 避免不必要的拷贝，保证参数不被修改
- **智能指针**: 使用unique_ptr演示现代C++内存管理

### 3. 访问控制设计
- `logCount` 使用 `protected` 访问权限，允许派生类访问但对外隐藏
- 公共接口通过 `public` 方法提供
- 实现细节通过适当的封装隐藏

## 编译和运行

### 使用VSCode
1. 按 `Ctrl+Shift+B` 编译项目
2. 按 `F5` 启动调试运行

### 手动编译
```bash
g++ -g -std=c++17 -Wall -Wextra main.cpp logger.cpp -o bin/logger_system.exe
```

### 运行程序
```bash
./bin/logger_system.exe
```

## 运行结果
程序会演示三种使用方式：
1. 直接使用派生类对象
2. 通过基类指针实现多态
3. 使用智能指针的现代C++方式

最终显示总的日志调用次数统计。

## 技术要点
- 面向对象设计原则的应用
- C++多态机制的实现
- 静态成员的正确使用
- const正确性的体现
- 现代C++特性的运用
