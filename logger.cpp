#include "logger.h"
#include <iostream>

// 静态成员变量定义
int Logger::logCount = 0;

// Logger基类实现
int Logger::getLogCount() {
    return logCount;
}

const char* Logger::getLoggerType() const {
    return "Base Logger";
}

// InfoLogger实现
void InfoLogger::log(const std::string& message) {
    logCount++;  // 增加日志调用计数
    std::cout << "[INFO]: " << message << std::endl;
}

const char* InfoLogger::getLoggerType() const {
    return "Info Logger";
}

// ErrorLogger实现
void ErrorLogger::log(const std::string& message) {
    logCount++;  // 增加日志调用计数
    std::cout << "[ERROR]: " << message << std::endl;
}

const char* ErrorLogger::getLoggerType() const {
    return "Error Logger";
}

// WarningLogger实现
void WarningLogger::log(const std::string& message) {
    logCount++;  // 增加日志调用计数
    std::cout << "[WARNING]: " << message << std::endl;
}

const char* WarningLogger::getLoggerType() const {
    return "Warning Logger";
}
