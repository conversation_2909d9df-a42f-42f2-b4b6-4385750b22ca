{"version": "0.2.0", "configurations": [{"name": "Debug Logger System", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/bin/logger_system.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "为 gdb 启用整齐打印", "text": "-enable-pretty-printing", "ignoreFailures": true}], "preLaunchTask": "build", "internalConsoleOptions": "openOnSessionStart", "console": "externalTerminal"}, {"name": "Run Logger System", "type": "cppdbg", "request": "launch", "program": "${workspaceFolder}/bin/logger_system.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}", "environment": [], "externalConsole": true, "MIMode": "gdb", "miDebuggerPath": "gdb", "preLaunchTask": "build", "internalConsoleOptions": "neverOpen", "console": "externalTerminal"}]}